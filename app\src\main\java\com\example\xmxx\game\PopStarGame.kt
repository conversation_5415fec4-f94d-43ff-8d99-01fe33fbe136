package com.example.xmxx.game

import kotlin.random.Random

typealias Board = List<MutableList<Int?>> // 外层 List 表示列（x 方向），内层 MutableList 表示该列的 y 方向自下而上

/**
 * 消灭星星（PopStar）核心逻辑（纯 Kotlin，无平台依赖）
 * - 网格为 width x height
 * - 颜色用 Int 表示（0..colors-1），null 表示空格
 * - 相邻判定为上下左右四连通
 * - 计分规则（按需可配置）：
 *      1) 每次消除得分：n^2 * 5（n 为一次消除的个数，n>=2 才能消除）
 *      2) 终局结算：若剩余 < 10，则加分 2000 - 剩余*5；否则 0 分
 */
object PopStarGame {
    data class Pos(val x: Int, val y: Int)



    // 创建新棋盘（按列存储，便于实现重力和列左移）
    fun newBoard(width: Int = 10, height: Int = 10, colors: Int = 5, seed: Long? = null): Board {
        val rnd = if (seed != null) Random(seed) else Random
        return List(width) {
            MutableList(height) { rnd.nextInt(colors) }
        }
    }

    // 获取某个坐标的颜色
    fun get(board: Board, x: Int, y: Int): Int? =
        if (x in board.indices && y in 0 until columnHeight(board)) board[x][y] else null

    fun width(board: Board): Int = board.size
    fun columnHeight(board: Board): Int = if (board.isNotEmpty()) board[0].size else 0

    // 寻找与 (x, y) 同色且相连的所有格子（四连通）
    fun findGroup(board: Board, x: Int, y: Int): Set<Pos> {
        val color = get(board, x, y) ?: return emptySet()
        val visited = mutableSetOf<Pos>()
        val stack = ArrayDeque<Pos>()
        stack.add(Pos(x, y))
        while (stack.isNotEmpty()) {
            val p = stack.removeLast()
            if (p in visited) continue
            val c = get(board, p.x, p.y) ?: continue
            if (c != color) continue
            visited += p
            // 四方向
            if (p.x > 0) stack.add(Pos(p.x - 1, p.y))
            if (p.x < width(board) - 1) stack.add(Pos(p.x + 1, p.y))
            if (p.y > 0) stack.add(Pos(p.x, p.y - 1))
            if (p.y < columnHeight(board) - 1) stack.add(Pos(p.x, p.y + 1))
        }
        return visited
    }

    // 一次消除的得分：n^2 * 5（n>=2）
    private fun scoreFor(n: Int): Int = if (n >= 2) n * n * 5 else 0

    // 剩余棋子数
    fun remainingCount(board: Board): Int {
        var cnt = 0
        val w = width(board); val h = columnHeight(board)
        for (x in 0 until w) for (y in 0 until h) if (get(board, x, y) != null) cnt++
        return cnt
    }

    // 应用一次点击操作；返回 (新的棋盘, 得分增量, 实际消除大小)
    fun applyMove(board: Board, x: Int, y: Int): Triple<Board, Int, Int> {
        val group = findGroup(board, x, y)
        if (group.size < 2) return Triple(board.map { it.toMutableList() }, 0, 0)

        // 复制棋盘
        val newBoard = board.map { it.toMutableList() }
        // 删除组内元素（置空）
        for ((gx, gy) in group) newBoard[gx][gy] = null
        // 列内重力（让非空下落）
        for (cx in newBoard.indices) {
            compactColumn(newBoard[cx])
        }
        // 左移空列
        val compacted = compactColumns(newBoard)

        val n = group.size
        val gain = scoreFor(n)
        return Triple(compacted, gain, n)
    }

    // 列内压缩：把非空元素沉到底部（y 小的为底）
    private fun compactColumn(col: MutableList<Int?>) {
        var write = 0
        for (read in col.indices) {
            val v = col[read]
            if (v != null) {
                if (write != read) {
                    col[write] = v
                    col[read] = null
                }
                write++
            }
        }
    }

    // 压缩列：移除全空列并将后续列整体左移
    private fun compactColumns(board: List<MutableList<Int?>>): Board {
        val keep = board.filter { col -> col.any { it != null } }
        val widthNeeded = board.size
        val height = columnHeight(board)
        val result = keep.toMutableList()
        // 添加空列填满宽度（理论上可能不需要，但保持矩形结构）
        while (result.size < widthNeeded) {
            result.add(MutableList(height) { null })
        }
        return result
    }

    // 是否还有可消除的组（>=2）
    fun hasMoves(board: Board): Boolean {
        val w = width(board)
        val h = columnHeight(board)
        for (x in 0 until w) for (y in 0 until h) {
            val c = get(board, x, y) ?: continue
            // 检查右或上即可（避免重复）
            if (x + 1 < w && get(board, x + 1, y) == c) return true
            if (y + 1 < h && get(board, x, y + 1) == c) return true
        }
        return false
    }

    // 序列化：按列->行，从 y=0 到 y=h-1；空为 '.'，颜色为 0..9（最多支持 10 种）
    fun serialize(board: Board): String {
        val w = width(board)
        val h = columnHeight(board)
        val sb = StringBuilder(w * h + w)
        for (x in 0 until w) {
            for (y in 0 until h) {
                val v = get(board, x, y)
                val ch = if (v == null) '.' else ('0'.code + (v % 10)).toChar()
                sb.append(ch)
            }
            if (x != w - 1) sb.append('|')
        }
        return sb.toString()
    }

    // 反序列化
    fun deserialize(data: String): Board {
        if (data.isBlank()) return newBoard()
        val cols = data.split('|')
        val height = cols.firstOrNull()?.length ?: 0
        val board = cols.map { colStr ->
            MutableList(height) { idx ->
                val ch = colStr[idx]
                when (ch) {
                    '.' -> null
                    in '0'..'9' -> (ch - '0')
                    else -> null
                }
            }
        }
        return board
    }
}

