/**
 * 拼多多批发网站商品搜索自动化脚本
 * 使用方法：在拼多多批发网站页面的浏览器控制台中执行此代码
 */

// 主要的搜索函数 - 带重试机制
async function searchPddGoods(page = 1, query = "糖果", retryCount = 0, maxRetries = 3) {
    const url = 'https://pifa.pinduoduo.com/pifa/search/queryGoods';

    // 请求头配置 - 这些通常由浏览器自动生成，但某些关键参数需要从页面获取
    const headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://pifa.pinduoduo.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': `https://pifa.pinduoduo.com/search?word=${encodeURIComponent(query)}`,
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': navigator.userAgent
    };

    // 尝试从页面获取anti-content参数（如果存在）
    function getAntiContent() {
        try {
            // 方法0: 优先使用手动设置的参数
            if (window._manualAntiContent) return window._manualAntiContent;

            // 方法1: 检查全局变量
            if (window['antiContent']) return window['antiContent'];
            if (window['_antiContent']) return window['_antiContent'];

            // 方法2: 从页面的meta标签获取
            const metaAnti = document.querySelector('meta[name="anti-content"]');
            if (metaAnti) return metaAnti.content;

            // 方法3: 从script标签中搜索anti-content
            const scripts = document.querySelectorAll('script');
            for (let script of scripts) {
                const content = script.textContent || script.innerHTML;
                const match = content.match(/["']anti-content["']\s*:\s*["']([^"']+)["']/);
                if (match) return match[1];

                // 搜索其他可能的模式
                const match2 = content.match(/antiContent\s*[:=]\s*["']([^"']+)["']/);
                if (match2) return match2[1];
            }

            // 方法4: 检查window对象的所有属性
            for (let key in window) {
                if (key.toLowerCase().includes('anti') && typeof window[key] === 'string' && window[key].length > 50) {
                    console.log(`🔍 找到可能的anti-content参数: ${key}`);
                    return window[key];
                }
            }

            // 方法5: 尝试从localStorage或sessionStorage获取
            const localAnti = localStorage.getItem('anti-content') || sessionStorage.getItem('anti-content');
            if (localAnti) return localAnti;

            console.warn('⚠️ 无法自动获取anti-content参数');
            return null;
        } catch (e) {
            console.warn('❌ 获取anti-content参数时出错:', e);
            return null;
        }
    }

    // 添加anti-content头（如果能获取到）
    const antiContent = getAntiContent();
    if (antiContent) {
        headers['anti-content'] = antiContent;
    }

    // 生成随机的rn参数（通常是32位十六进制字符串）
    function generateRn() {
        return Array.from({length: 32}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    }

    // 请求体数据
    const requestData = {
        "page": page,
        "size": 20,
        "sort": 0,
        "query": query,
        "propertyItems": [],
        "rn": generateRn()
    };

    try {
        console.log(`🔍 正在搜索第 ${page} 页，关键词：${query}${retryCount > 0 ? ` (重试 ${retryCount}/${maxRetries})` : ''}`);
        console.log('📤 请求数据:', requestData);
        if (antiContent) {
            console.log('🔐 使用anti-content参数');
        } else {
            console.warn('⚠️ 未找到anti-content参数，可能会被限制');
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestData),
            credentials: 'include' // 包含cookies，这很重要
        });

        if (!response.ok) {
            // 特殊处理429错误
            if (response.status === 429) {
                const retryAfter = response.headers.get('Retry-After') || 5;
                console.warn(`⚠️ 请求过于频繁 (429)，建议等待 ${retryAfter} 秒后重试`);

                if (retryCount < maxRetries) {
                    const waitTime = Math.max(retryAfter * 1000, 5000 + retryCount * 2000);
                    console.log(`⏳ 等待 ${waitTime/1000} 秒后自动重试...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    return searchPddGoods(page, query, retryCount + 1, maxRetries);
                }
            }
            throw new Error(`HTTP错误! 状态: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`✅ 第 ${page} 页数据获取成功:`, data);
        return data;

    } catch (error) {
        if (error.message.includes('429') && retryCount < maxRetries) {
            console.log(`🔄 429错误，${3 + retryCount * 2}秒后重试...`);
            await new Promise(resolve => setTimeout(resolve, (3 + retryCount * 2) * 1000));
            return searchPddGoods(page, query, retryCount + 1, maxRetries);
        }

        console.error(`❌ 请求失败:`, error);
        throw error;
    }
}

// 手动设置anti-content参数的函数
function setAntiContent(antiContentValue) {
    window._manualAntiContent = antiContentValue;
    console.log('✅ 已手动设置anti-content参数');
    console.log('💡 现在可以尝试重新发送请求');
}

// 批量搜索多页数据 - 增强版
async function searchMultiplePages(startPage = 1, endPage = 5, query = "糖果", delay = 2000) {
    const results = [];

    console.log(`🚀 开始批量搜索，页数范围：${startPage}-${endPage}，关键词：${query}`);

    for (let page = startPage; page <= endPage; page++) {
        try {
            const result = await searchPddGoods(page, query);
            results.push({
                page: page,
                data: result,
                timestamp: new Date().toISOString()
            });

            // 添加延迟避免请求过于频繁
            if (page < endPage) {
                console.log(`⏳ 等待 ${delay}ms 后继续下一页...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

        } catch (error) {
            console.error(`❌ 第 ${page} 页搜索失败:`, error);
            results.push({
                page: page,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    console.log(`🎉 批量搜索完成！共处理 ${results.length} 页`);
    return results;
}

// 提取商品信息的辅助函数
function extractGoodsInfo(searchResult) {
    if (!searchResult.data || !searchResult.data.result || !searchResult.data.result.goods) {
        console.warn(`⚠️ 第 ${searchResult.page} 页没有商品数据`);
        return [];
    }

    return searchResult.data.result.goods.map(item => ({
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        price: item.price,
        sales: item.sales,
        shopName: item.shopName,
        imageUrl: item.imageUrl,
        page: searchResult.page
    }));
}

// 导出数据为CSV格式
function exportToCSV(goodsList, filename = 'pdd_goods.csv') {
    if (!goodsList || goodsList.length === 0) {
        console.warn('没有数据可导出');
        return;
    }

    const headers = ['商品ID', '商品名称', '价格', '销量', '店铺名称', '图片链接', '页码'];
    const csvContent = [
        headers.join(','),
        ...goodsList.map(item => [
            item.goodsId,
            `"${item.goodsName}"`,
            item.price,
            item.sales,
            `"${item.shopName}"`,
            item.imageUrl,
            item.page
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    console.log(`📁 数据已导出为 ${filename}`);
}

// 使用示例和说明
console.log(`
🎯 拼多多批发搜索脚本已加载！（增强版 - 支持429错误处理）

📖 使用方法：

1. 搜索单页：
   searchPddGoods(1, "糖果");

2. 搜索多页（推荐2秒延迟）：
   searchMultiplePages(1, 5, "糖果", 2000);

3. 完整使用示例：
   // 搜索并提取商品信息
   const results = await searchMultiplePages(1, 3, "糖果", 3000);
   const allGoods = results.flatMap(extractGoodsInfo);
   console.log('所有商品:', allGoods);

   // 导出为CSV文件
   exportToCSV(allGoods, '糖果商品.csv');

4. 快速搜索当前页面关键词：
   const urlParams = new URLSearchParams(window.location.search);
   const currentQuery = decodeURIComponent(urlParams.get('word') || '糖果');
   searchPddGoods(1, currentQuery);

🚨 解决429错误的方法：

方法1 - 手动设置anti-content参数：
1. 在浏览器开发者工具的Network标签中找到正常的请求
2. 复制请求头中的anti-content值
3. 执行：setAntiContent("你复制的anti-content值");
4. 然后重新发送请求

方法2 - 增加延迟时间：
   searchMultiplePages(1, 3, "糖果", 5000); // 5秒延迟

方法3 - 刷新页面后重试：
   location.reload(); // 刷新页面获取新的参数

⚠️  重要注意事项：
- ✅ 请确保在拼多多批发网站页面执行此脚本
- ✅ 脚本会自动重试429错误（最多3次）
- ✅ 建议使用2-5秒的请求延迟
- 🔐 anti-content参数是关键的反爬参数，缺失会导致429错误
- ⏳ 遇到429错误时会自动等待并重试
- 🔄 支持手动设置anti-content参数

🔧 参数说明：
- page: 页码（从1开始）
- query: 搜索关键词
- delay: 请求间隔时间（毫秒，推荐2000-5000）
- startPage/endPage: 批量搜索的页码范围

🛠️ 调试命令：
- setAntiContent("参数值") - 手动设置anti-content
- window._manualAntiContent - 查看当前设置的参数
`);

// 自动检测当前页面环境
if (window.location.hostname === 'pifa.pinduoduo.com') {
    console.log('✅ 检测到拼多多批发网站环境，脚本可以正常使用');

    // 尝试获取当前页面的搜索关键词
    const urlParams = new URLSearchParams(window.location.search);
    const currentQuery = urlParams.get('word');
    if (currentQuery) {
        console.log(`🔍 检测到当前搜索关键词: ${decodeURIComponent(currentQuery)}`);
        console.log(`💡 快速开始: searchPddGoods(1, "${decodeURIComponent(currentQuery)}")`);
    }
} else {
    console.warn('⚠️  当前不在拼多多批发网站，某些功能可能无法正常工作');
}