/**
 * 拼多多批发网站商品搜索自动化脚本 v2.0
 * 使用方法：在拼多多批发网站页面的浏览器控制台中执行此代码
 * 新版本：利用网站内部机制自动生成anti-content参数
 */

// 全局变量存储捕获的参数
window._capturedAntiContent = null;
window._capturedHeaders = null;

// 全面的网络请求拦截机制
const originalFetch = window.fetch;
const originalXHROpen = XMLHttpRequest.prototype.open;
const originalXHRSend = XMLHttpRequest.prototype.send;
let isIntercepting = true;

// 拦截fetch请求
window.fetch = function(...args) {
    const [url, options] = args;

    // 防止无限递归：如果是我们自己发起的请求，直接调用原始fetch
    if (options && options._isOurRequest) {
        delete options._isOurRequest;
        return originalFetch.apply(this, args);
    }

    // 如果是queryGoods请求，捕获其参数
    if (isIntercepting && typeof url === 'string' && url.includes('/pifa/search/queryGoods')) {
        console.log('🎯 [Fetch] 捕获到网站内部的queryGoods请求');

        if (options && options.headers) {
            const headers = options.headers;
            console.log('📋 请求头:', headers);

            if (headers['anti-content']) {
                window._capturedAntiContent = headers['anti-content'];
                window._capturedHeaders = {...headers};
                console.log('✅ 成功捕获anti-content参数:', headers['anti-content'].substring(0, 50) + '...');
            }
        }
    }

    return originalFetch.apply(this, args);
};

// 拦截XMLHttpRequest
XMLHttpRequest.prototype.open = function(method, url, ...args) {
    this._url = url;
    this._method = method;
    return originalXHROpen.apply(this, [method, url, ...args]);
};

XMLHttpRequest.prototype.send = function(data) {
    if (isIntercepting && this._url && this._url.includes('/pifa/search/queryGoods')) {
        console.log('🎯 [XHR] 捕获到网站内部的queryGoods请求');

        // 尝试从请求头中获取anti-content
        const antiContent = this.getRequestHeader('anti-content');
        if (antiContent) {
            window._capturedAntiContent = antiContent;
            console.log('✅ 成功捕获anti-content参数:', antiContent.substring(0, 50) + '...');
        }

        console.log('📋 XHR请求数据:', data);
    }

    return originalXHRSend.apply(this, arguments);
};

// 从开发者工具Network面板提取参数的辅助函数
function extractFromDevTools() {
    console.log(`
🛠️ 手动提取anti-content参数的步骤：

1. 打开开发者工具 (F12)
2. 切换到 Network 标签
3. 在页面上手动搜索一次
4. 找到 'queryGoods' 请求
5. 点击该请求，查看 Request Headers
6. 复制 'anti-content' 的值
7. 执行以下命令：

   setAntiContent("你复制的anti-content值");

💡 或者直接在控制台执行：
   copyAntiContentFromNetwork();
`);
}

// 尝试从Network面板自动提取（实验性功能）
function copyAntiContentFromNetwork() {
    try {
        // 这个功能需要用户手动操作，因为无法直接访问DevTools的Network数据
        console.log('📋 请按以下步骤操作：');
        console.log('1. 在Network面板中找到最新的queryGoods请求');
        console.log('2. 右键点击该请求 → Copy → Copy as cURL');
        console.log('3. 将复制的内容粘贴到控制台，然后执行：');
        console.log('   parseAntiContentFromCurl("粘贴的cURL命令");');
    } catch (error) {
        console.error('❌ 自动提取失败:', error);
    }
}

// 从cURL命令中解析anti-content参数
function parseAntiContentFromCurl(curlCommand) {
    try {
        const match = curlCommand.match(/-H\s+['"]anti-content:\s*([^'"]+)['"]/);
        if (match) {
            const antiContent = match[1];
            setAntiContent(antiContent);
            console.log('✅ 成功从cURL命令中提取anti-content参数！');
            return antiContent;
        } else {
            console.error('❌ 未在cURL命令中找到anti-content参数');
            return null;
        }
    } catch (error) {
        console.error('❌ 解析cURL命令失败:', error);
        return null;
    }
}

// 主要的搜索函数 - 带重试机制
async function searchPddGoods(page = 1, query = "糖果", retryCount = 0, maxRetries = 3) {
    const url = 'https://pifa.pinduoduo.com/pifa/search/queryGoods';

    // 请求头配置 - 这些通常由浏览器自动生成，但某些关键参数需要从页面获取
    const headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://pifa.pinduoduo.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': `https://pifa.pinduoduo.com/search?word=${encodeURIComponent(query)}`,
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': navigator.userAgent
    };

    // 尝试从页面获取anti-content参数（如果存在）
    function getAntiContent() {
        try {
            // 方法0: 优先使用手动设置的参数
            if (window._manualAntiContent) return window._manualAntiContent;

            // 方法0.5: 使用捕获到的参数
            if (window._capturedAntiContent) {
                console.log('🎯 使用捕获到的anti-content参数');
                return window._capturedAntiContent;
            }

            // 方法1: 检查全局变量
            if (window['antiContent']) return window['antiContent'];
            if (window['_antiContent']) return window['_antiContent'];

            // 方法2: 从页面的meta标签获取
            const metaAnti = document.querySelector('meta[name="anti-content"]');
            if (metaAnti) return metaAnti.content;

            // 方法3: 从script标签中搜索anti-content
            const scripts = document.querySelectorAll('script');
            for (let script of scripts) {
                const content = script.textContent || script.innerHTML;
                const match = content.match(/["']anti-content["']\s*:\s*["']([^"']+)["']/);
                if (match) return match[1];

                // 搜索其他可能的模式
                const match2 = content.match(/antiContent\s*[:=]\s*["']([^"']+)["']/);
                if (match2) return match2[1];
            }

            // 方法4: 检查window对象的所有属性
            for (let key in window) {
                if (key.toLowerCase().includes('anti') && typeof window[key] === 'string' && window[key].length > 50) {
                    console.log(`🔍 找到可能的anti-content参数: ${key}`);
                    return window[key];
                }
            }

            // 方法5: 尝试从localStorage或sessionStorage获取
            const localAnti = localStorage.getItem('anti-content') || sessionStorage.getItem('anti-content');
            if (localAnti) return localAnti;

            console.warn('⚠️ 无法自动获取anti-content参数');
            return null;
        } catch (e) {
            console.warn('❌ 获取anti-content参数时出错:', e);
            return null;
        }
    }

    // 添加anti-content头（如果能获取到）
    const antiContent = getAntiContent();
    if (antiContent) {
        headers['anti-content'] = antiContent;
    }

    // 生成随机的rn参数（通常是32位十六进制字符串）
    function generateRn() {
        return Array.from({length: 32}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    }

    // 请求体数据
    const requestData = {
        "page": page,
        "size": 20,
        "sort": 0,
        "query": query,
        "propertyItems": [],
        "rn": generateRn()
    };

    try {
        console.log(`🔍 正在搜索第 ${page} 页，关键词：${query}${retryCount > 0 ? ` (重试 ${retryCount}/${maxRetries})` : ''}`);
        console.log('📤 请求数据:', requestData);
        if (antiContent) {
            console.log('🔐 使用anti-content参数');
        } else {
            console.warn('⚠️ 未找到anti-content参数，可能会被限制');
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestData),
            credentials: 'include', // 包含cookies，这很重要
            _isOurRequest: true // 标记这是我们发起的请求，避免被拦截器处理
        });

        if (!response.ok) {
            // 特殊处理429错误
            if (response.status === 429) {
                const retryAfter = response.headers.get('Retry-After') || 5;
                console.warn(`⚠️ 请求过于频繁 (429)，建议等待 ${retryAfter} 秒后重试`);

                if (retryCount < maxRetries) {
                    const waitTime = Math.max(retryAfter * 1000, 5000 + retryCount * 2000);
                    console.log(`⏳ 等待 ${waitTime/1000} 秒后自动重试...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    return searchPddGoods(page, query, retryCount + 1, maxRetries);
                }
            }
            throw new Error(`HTTP错误! 状态: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`✅ 第 ${page} 页数据获取成功:`, data);
        return data;

    } catch (error) {
        if (error.message.includes('429') && retryCount < maxRetries) {
            console.log(`🔄 429错误，${3 + retryCount * 2}秒后重试...`);
            await new Promise(resolve => setTimeout(resolve, (3 + retryCount * 2) * 1000));
            return searchPddGoods(page, query, retryCount + 1, maxRetries);
        }

        console.error(`❌ 请求失败:`, error);
        throw error;
    }
}

// 触发网站内部搜索以获取anti-content参数
async function triggerInternalSearch(query = "糖果") {
    console.log('🔄 正在触发网站内部搜索以获取参数...');

    try {
        // 方法1: 尝试触发页面上的搜索按钮
        const searchButton = document.querySelector('button[type="submit"]') ||
                           document.querySelector('.search-btn') ||
                           document.querySelector('[class*="search"]') ||
                           document.querySelector('button');

        if (searchButton) {
            console.log('🎯 找到搜索按钮，尝试触发点击');
            searchButton.click();
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 方法2: 尝试修改搜索框并触发搜索
        const searchInput = document.querySelector('input[type="text"]') ||
                          document.querySelector('input[placeholder*="搜索"]') ||
                          document.querySelector('input');

        if (searchInput) {
            console.log('🎯 找到搜索框，尝试触发搜索');
            searchInput.value = query;
            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
            searchInput.dispatchEvent(new Event('change', { bubbles: true }));

            // 尝试按回车
            searchInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 方法3: 尝试调用页面上可能存在的搜索函数
        if (window.search) {
            console.log('🎯 找到全局search函数，尝试调用');
            window.search(query);
        }

        // 等待一段时间让请求完成
        await new Promise(resolve => setTimeout(resolve, 2000));

        if (window._capturedAntiContent) {
            console.log('✅ 成功获取到anti-content参数！');
            return true;
        } else {
            console.log('⚠️ 未能获取到anti-content参数，请手动触发一次搜索');
            return false;
        }

    } catch (error) {
        console.error('❌ 触发内部搜索时出错:', error);
        return false;
    }
}

// 手动设置anti-content参数的函数
function setAntiContent(antiContentValue) {
    window._manualAntiContent = antiContentValue;
    console.log('✅ 已手动设置anti-content参数');
    console.log('💡 现在可以尝试重新发送请求');
}

// 智能搜索函数 - 自动获取参数后搜索
async function smartSearch(page = 1, query = "糖果") {
    console.log('🧠 启动智能搜索模式...');

    // 检查是否已有anti-content参数
    if (!window._capturedAntiContent && !window._manualAntiContent) {
        console.log('🔄 未检测到anti-content参数，尝试自动获取...');

        const success = await triggerInternalSearch(query);
        if (!success) {
            console.log('❌ 自动获取失败，请按以下步骤手动操作：');
            console.log('1. 在页面上手动搜索一次任意关键词');
            console.log('2. 然后重新运行 smartSearch() 或 searchPddGoods()');
            return null;
        }
    }

    // 现在尝试搜索
    return await searchPddGoods(page, query);
}

// 批量搜索多页数据 - 增强版
async function searchMultiplePages(startPage = 1, endPage = 5, query = "糖果", delay = 2000) {
    const results = [];

    console.log(`🚀 开始批量搜索，页数范围：${startPage}-${endPage}，关键词：${query}`);

    for (let page = startPage; page <= endPage; page++) {
        try {
            const result = await searchPddGoods(page, query);
            results.push({
                page: page,
                data: result,
                timestamp: new Date().toISOString()
            });

            // 添加延迟避免请求过于频繁
            if (page < endPage) {
                console.log(`⏳ 等待 ${delay}ms 后继续下一页...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

        } catch (error) {
            console.error(`❌ 第 ${page} 页搜索失败:`, error);
            results.push({
                page: page,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    console.log(`🎉 批量搜索完成！共处理 ${results.length} 页`);
    return results;
}

// 提取商品信息的辅助函数
function extractGoodsInfo(searchResult) {
    if (!searchResult.data || !searchResult.data.result || !searchResult.data.result.goods) {
        console.warn(`⚠️ 第 ${searchResult.page} 页没有商品数据`);
        return [];
    }

    return searchResult.data.result.goods.map(item => ({
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        price: item.price,
        sales: item.sales,
        shopName: item.shopName,
        imageUrl: item.imageUrl,
        page: searchResult.page
    }));
}

// 导出数据为CSV格式
function exportToCSV(goodsList, filename = 'pdd_goods.csv') {
    if (!goodsList || goodsList.length === 0) {
        console.warn('没有数据可导出');
        return;
    }

    const headers = ['商品ID', '商品名称', '价格', '销量', '店铺名称', '图片链接', '页码'];
    const csvContent = [
        headers.join(','),
        ...goodsList.map(item => [
            item.goodsId,
            `"${item.goodsName}"`,
            item.price,
            item.sales,
            `"${item.shopName}"`,
            item.imageUrl,
            item.page
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    console.log(`📁 数据已导出为 ${filename}`);
}

// 使用示例和说明
console.log(`
🎯 拼多多批发搜索脚本已加载！（v2.0 - 智能参数获取版）

📖 推荐使用方法：

🌟 1. 智能搜索（推荐）：
   smartSearch(1, "糖果");  // 自动获取参数并搜索

🌟 2. 智能批量搜索：
   // 先获取参数
   await triggerInternalSearch("糖果");
   // 然后批量搜索
   const results = await searchMultiplePages(1, 3, "糖果", 3000);

📖 传统使用方法：

3. 手动搜索单页：
   searchPddGoods(1, "糖果");

4. 手动批量搜索：
   searchMultiplePages(1, 5, "糖果", 2000);

5. 完整工作流：
   // 搜索并提取商品信息
   const results = await searchMultiplePages(1, 3, "糖果", 3000);
   const allGoods = results.flatMap(extractGoodsInfo);
   console.log('所有商品:', allGoods);

   // 导出为CSV文件
   exportToCSV(allGoods, '糖果商品.csv');

🚨 解决429错误的方法（按推荐程度排序）：

🌟 方法1 - 手动搜索触发（最可靠）：
1. 在拼多多页面上手动搜索一次任意关键词
2. 脚本会自动拦截并捕获anti-content参数
3. 看到"✅ 成功捕获anti-content参数"提示后
4. 使用 searchPddGoods(1, "糖果") 或 smartSearch(1, "糖果")

🌟 方法2 - 从开发者工具复制（100%有效）：
1. 按F12打开开发者工具 → Network标签
2. 在页面上手动搜索一次
3. 找到 'queryGoods' 请求，点击查看详情
4. 在Request Headers中找到 'anti-content'
5. 复制该值，执行：setAntiContent("复制的值");

🌟 方法3 - 一键cURL解析：
1. 在Network面板中右键queryGoods请求
2. 选择 Copy → Copy as cURL
3. 执行：parseAntiContentFromCurl("粘贴cURL命令");

方法4 - 自动触发（可能不稳定）：
   triggerInternalSearch("糖果");  // 尝试自动触发

⚠️  重要注意事项：
- ✅ 脚本已自动拦截网站内部请求，会捕获anti-content参数
- ✅ 推荐使用 smartSearch() 函数，它会自动处理参数获取
- ✅ 脚本会自动重试429错误（最多3次）
- 🔐 anti-content参数现在可以自动获取
- ⏳ 遇到429错误时会自动等待并重试
- 🎯 新版本利用网站内部机制生成参数

🔧 参数说明：
- page: 页码（从1开始）
- query: 搜索关键词
- delay: 请求间隔时间（毫秒，推荐2000-5000）
- startPage/endPage: 批量搜索的页码范围

🛠️ 调试命令大全：

基础搜索：
- smartSearch(page, query) - 智能搜索（推荐）
- searchPddGoods(page, query) - 直接搜索
- searchMultiplePages(start, end, query, delay) - 批量搜索

参数管理：
- setAntiContent("参数值") - 手动设置anti-content
- window._capturedAntiContent - 查看捕获到的参数
- window._capturedHeaders - 查看完整请求头
- window._manualAntiContent - 查看手动设置的参数

参数获取：
- extractFromDevTools() - 显示手动提取步骤
- copyAntiContentFromNetwork() - 显示复制步骤
- parseAntiContentFromCurl("cURL命令") - 从cURL解析参数
- triggerInternalSearch(query) - 尝试自动触发

数据处理：
- extractGoodsInfo(results) - 提取商品信息
- exportToCSV(goodsList, filename) - 导出CSV文件

💡 最佳实践：
1. 先在页面手动搜索一次让脚本捕获参数
2. 使用 smartSearch() 进行搜索
3. 如果还是429错误，使用 extractFromDevTools() 查看手动步骤
`);

// 自动检测当前页面环境
if (window.location.hostname === 'pifa.pinduoduo.com') {
    console.log('✅ 检测到拼多多批发网站环境，脚本可以正常使用');

    // 尝试获取当前页面的搜索关键词
    const urlParams = new URLSearchParams(window.location.search);
    const currentQuery = urlParams.get('word');
    if (currentQuery) {
        console.log(`🔍 检测到当前搜索关键词: ${decodeURIComponent(currentQuery)}`);
        console.log(`💡 快速开始: searchPddGoods(1, "${decodeURIComponent(currentQuery)}")`);
    }
} else {
    console.warn('⚠️  当前不在拼多多批发网站，某些功能可能无法正常工作');
}