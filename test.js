/**
 * 拼多多批发网站商品搜索自动化脚本
 * 使用方法：在拼多多批发网站页面的浏览器控制台中执行此代码
 */

// 主要的搜索函数
async function searchPddGoods(page = 1, query = "糖果") {
    const url = 'https://pifa.pinduoduo.com/pifa/search/queryGoods';

    // 请求头配置 - 这些通常由浏览器自动生成，但某些关键参数需要从页面获取
    const headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://pifa.pinduoduo.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': `https://pifa.pinduoduo.com/search?word=${encodeURIComponent(query)}`,
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': navigator.userAgent
    };

    // 尝试从页面获取anti-content参数（如果存在）
    function getAntiContent() {
        // 尝试从页面的script标签或全局变量中获取anti-content
        try {
            // 检查是否有全局的anti-content变量
            if (window.antiContent) return window.antiContent;

            // 尝试从页面的meta标签获取
            const metaAnti = document.querySelector('meta[name="anti-content"]');
            if (metaAnti) return metaAnti.content;

            // 如果都没有，返回null，让浏览器自动处理
            return null;
        } catch (e) {
            console.warn('无法获取anti-content参数，将依赖浏览器自动处理');
            return null;
        }
    }

    // 添加anti-content头（如果能获取到）
    const antiContent = getAntiContent();
    if (antiContent) {
        headers['anti-content'] = antiContent;
    }

    // 生成随机的rn参数（通常是32位十六进制字符串）
    function generateRn() {
        return Array.from({length: 32}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    }

    // 请求体数据
    const requestData = {
        "page": page,
        "size": 20,
        "sort": 0,
        "query": query,
        "propertyItems": [],
        "rn": generateRn()
    };

    try {
        console.log(`🔍 正在搜索第 ${page} 页，关键词：${query}`);
        console.log('📤 请求数据:', requestData);

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestData),
            credentials: 'include' // 包含cookies，这很重要
        });

        if (!response.ok) {
            throw new Error(`HTTP错误! 状态: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`✅ 第 ${page} 页数据获取成功:`, data);
        return data;

    } catch (error) {
        console.error(`❌ 请求失败:`, error);
        throw error;
    }
}

// 批量搜索多页数据
async function searchMultiplePages(startPage = 1, endPage = 5, query = "糖果", delay = 1000) {
    const results = [];

    console.log(`🚀 开始批量搜索，页数范围：${startPage}-${endPage}，关键词：${query}`);

    for (let page = startPage; page <= endPage; page++) {
        try {
            const result = await searchPddGoods(page, query);
            results.push({
                page: page,
                data: result,
                timestamp: new Date().toISOString()
            });

            // 添加延迟避免请求过于频繁
            if (page < endPage) {
                console.log(`⏳ 等待 ${delay}ms 后继续下一页...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

        } catch (error) {
            console.error(`❌ 第 ${page} 页搜索失败:`, error);
            results.push({
                page: page,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    console.log(`🎉 批量搜索完成！共处理 ${results.length} 页`);
    return results;
}

// 提取商品信息的辅助函数
function extractGoodsInfo(searchResult) {
    if (!searchResult.data || !searchResult.data.result || !searchResult.data.result.goods) {
        console.warn(`⚠️ 第 ${searchResult.page} 页没有商品数据`);
        return [];
    }

    return searchResult.data.result.goods.map(item => ({
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        price: item.price,
        sales: item.sales,
        shopName: item.shopName,
        imageUrl: item.imageUrl,
        page: searchResult.page
    }));
}

// 导出数据为CSV格式
function exportToCSV(goodsList, filename = 'pdd_goods.csv') {
    if (!goodsList || goodsList.length === 0) {
        console.warn('没有数据可导出');
        return;
    }

    const headers = ['商品ID', '商品名称', '价格', '销量', '店铺名称', '图片链接', '页码'];
    const csvContent = [
        headers.join(','),
        ...goodsList.map(item => [
            item.goodsId,
            `"${item.goodsName}"`,
            item.price,
            item.sales,
            `"${item.shopName}"`,
            item.imageUrl,
            item.page
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    console.log(`📁 数据已导出为 ${filename}`);
}

// 使用示例和说明
console.log(`
🎯 拼多多批发搜索脚本已加载！

📖 使用方法：

1. 搜索单页：
   searchPddGoods(1, "糖果");

2. 搜索多页：
   searchMultiplePages(1, 5, "糖果", 1000);

3. 完整使用示例：
   // 搜索并提取商品信息
   const results = await searchMultiplePages(1, 3, "糖果");
   const allGoods = results.flatMap(extractGoodsInfo);
   console.log('所有商品:', allGoods);

   // 导出为CSV文件
   exportToCSV(allGoods, '糖果商品.csv');

4. 快速搜索当前页面关键词：
   // 自动从当前页面URL获取搜索关键词
   const urlParams = new URLSearchParams(window.location.search);
   const currentQuery = decodeURIComponent(urlParams.get('word') || '糖果');
   searchPddGoods(1, currentQuery);

⚠️  重要注意事项：
- ✅ 请确保在拼多多批发网站页面执行此脚本
- ✅ 脚本会自动使用当前页面的cookies和用户代理
- ✅ 建议设置适当的请求延迟避免被限制
- ⚠️  anti-content参数由网站动态生成，脚本会尝试自动获取
- ⚠️  如果遇到反爬限制，请降低请求频率或手动刷新页面

🔧 参数说明：
- page: 页码（从1开始）
- query: 搜索关键词
- delay: 请求间隔时间（毫秒，建议1000-3000）
- startPage/endPage: 批量搜索的页码范围
`);

// 自动检测当前页面环境
if (window.location.hostname === 'pifa.pinduoduo.com') {
    console.log('✅ 检测到拼多多批发网站环境，脚本可以正常使用');

    // 尝试获取当前页面的搜索关键词
    const urlParams = new URLSearchParams(window.location.search);
    const currentQuery = urlParams.get('word');
    if (currentQuery) {
        console.log(`🔍 检测到当前搜索关键词: ${decodeURIComponent(currentQuery)}`);
        console.log(`💡 快速开始: searchPddGoods(1, "${decodeURIComponent(currentQuery)}")`);
    }
} else {
    console.warn('⚠️  当前不在拼多多批发网站，某些功能可能无法正常工作');
}