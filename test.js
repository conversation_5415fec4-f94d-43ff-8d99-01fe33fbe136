/**
 * 拼多多批发网站商品搜索自动化脚本 v3.0
 * 使用方法：在拼多多批发网站页面的浏览器控制台中执行此代码
 * 新版本：简化版，专注于手动参数设置，避免拦截器问题
 */

// 全局变量存储参数
window._manualAntiContent = null;

// 从开发者工具Network面板提取参数的辅助函数
function extractFromDevTools() {
    console.log(`
🛠️ 获取anti-content参数的详细步骤：

📋 方法1 - 从Network面板复制（推荐）：
1. 按F12打开开发者工具
2. 切换到 Network 标签
3. 清空网络记录（点击🚫图标）
4. 在页面上手动搜索一次任意关键词
5. 在Network中找到 'queryGoods' 请求（通常是最新的）
6. 点击该请求，查看 Request Headers
7. 找到 'anti-content' 字段，复制其值
8. 执行：setAntiContent("复制的值");

📋 方法2 - 一键cURL解析：
1. 在Network面板中右键 'queryGoods' 请求
2. 选择 Copy → Copy as cURL (bash)
3. 执行：parseAntiContentFromCurl(\`粘贴的cURL命令\`);

💡 提示：anti-content参数通常是一个很长的字符串，以字母和数字组成
`);
}

// 从cURL命令中解析anti-content参数
function parseAntiContentFromCurl(curlCommand) {
    try {
        console.log('🔍 正在解析cURL命令...');

        // 多种可能的anti-content格式匹配
        const patterns = [
            /-H\s+['"]anti-content:\s*([^'"]+)['"]/i,
            /-H\s+['"]anti-content:\s*([^'"\\s]+)/i,
            /--header\s+['"]anti-content:\s*([^'"]+)['"]/i,
            /'anti-content:\s*([^']+)'/i,
            /"anti-content:\s*([^"]+)"/i
        ];

        for (let pattern of patterns) {
            const match = curlCommand.match(pattern);
            if (match && match[1]) {
                const antiContent = match[1].trim();
                if (antiContent.length > 10) { // 确保不是空值
                    setAntiContent(antiContent);
                    console.log('✅ 成功从cURL命令中提取anti-content参数！');
                    console.log('� 参数长度:', antiContent.length);
                    console.log('� 参数预览:', antiContent.substring(0, 50) + '...');
                    return antiContent;
                }
            }
        }

        console.error('❌ 未在cURL命令中找到anti-content参数');
        console.log('� 请确保：');
        console.log('1. cURL命令是从queryGoods请求复制的');
        console.log('2. 请求包含完整的headers');
        console.log('3. 尝试重新复制cURL命令');
        return null;

    } catch (error) {
        console.error('❌ 解析cURL命令失败:', error);
        return null;
    }
}

// 验证anti-content参数格式
function validateAntiContent(antiContent) {
    if (!antiContent || typeof antiContent !== 'string') {
        return { valid: false, reason: '参数为空或格式错误' };
    }

    if (antiContent.length < 50) {
        return { valid: false, reason: '参数长度过短，可能不是有效的anti-content' };
    }

    if (antiContent.length > 2000) {
        return { valid: false, reason: '参数长度过长，可能包含了额外内容' };
    }

    // 检查是否包含常见的anti-content特征
    const hasValidChars = /^[a-zA-Z0-9+/=_-]+$/.test(antiContent);
    if (!hasValidChars) {
        return { valid: false, reason: '参数包含无效字符' };
    }

    return { valid: true, reason: '参数格式正确' };
}

// 主要的搜索函数 - 带重试机制
async function searchPddGoods(page = 1, query = "糖果", retryCount = 0, maxRetries = 3) {
    const url = 'https://pifa.pinduoduo.com/pifa/search/queryGoods';

    // 请求头配置 - 这些通常由浏览器自动生成，但某些关键参数需要从页面获取
    const headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://pifa.pinduoduo.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': `https://pifa.pinduoduo.com/search?word=${encodeURIComponent(query)}`,
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': navigator.userAgent
    };

    // 获取anti-content参数
    function getAntiContent() {
        try {
            // 优先使用手动设置的参数
            if (window._manualAntiContent) {
                console.log('🔐 使用手动设置的anti-content参数');
                return window._manualAntiContent;
            }

            // 检查全局变量（备用方案）
            if (window['antiContent']) return window['antiContent'];
            if (window['_antiContent']) return window['_antiContent'];

            // 方法2: 从页面的meta标签获取
            const metaAnti = document.querySelector('meta[name="anti-content"]');
            if (metaAnti) return metaAnti.content;

            // 方法3: 从script标签中搜索anti-content
            const scripts = document.querySelectorAll('script');
            for (let script of scripts) {
                const content = script.textContent || script.innerHTML;
                const match = content.match(/["']anti-content["']\s*:\s*["']([^"']+)["']/);
                if (match) return match[1];

                // 搜索其他可能的模式
                const match2 = content.match(/antiContent\s*[:=]\s*["']([^"']+)["']/);
                if (match2) return match2[1];
            }

            // 方法4: 检查window对象的所有属性
            for (let key in window) {
                if (key.toLowerCase().includes('anti') && typeof window[key] === 'string' && window[key].length > 50) {
                    console.log(`🔍 找到可能的anti-content参数: ${key}`);
                    return window[key];
                }
            }

            // 方法5: 尝试从localStorage或sessionStorage获取
            const localAnti = localStorage.getItem('anti-content') || sessionStorage.getItem('anti-content');
            if (localAnti) return localAnti;

            console.warn('⚠️ 无法自动获取anti-content参数');
            return null;
        } catch (e) {
            console.warn('❌ 获取anti-content参数时出错:', e);
            return null;
        }
    }

    // 添加anti-content头（如果能获取到）
    const antiContent = getAntiContent();
    if (antiContent) {
        headers['anti-content'] = antiContent;
    }

    // 生成随机的rn参数（通常是32位十六进制字符串）
    function generateRn() {
        return Array.from({length: 32}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    }

    // 请求体数据
    const requestData = {
        "page": page,
        "size": 20,
        "sort": 0,
        "query": query,
        "propertyItems": [],
        "rn": generateRn()
    };

    try {
        console.log(`🔍 正在搜索第 ${page} 页，关键词：${query}${retryCount > 0 ? ` (重试 ${retryCount}/${maxRetries})` : ''}`);
        console.log('📤 请求数据:', requestData);
        if (antiContent) {
            console.log('🔐 使用anti-content参数');
        } else {
            console.warn('⚠️ 未找到anti-content参数，可能会被限制');
        }

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestData),
            credentials: 'include', // 包含cookies，这很重要
            _isOurRequest: true // 标记这是我们发起的请求，避免被拦截器处理
        });

        if (!response.ok) {
            // 特殊处理429错误
            if (response.status === 429) {
                const retryAfter = response.headers.get('Retry-After') || 5;
                console.warn(`⚠️ 请求过于频繁 (429)，建议等待 ${retryAfter} 秒后重试`);

                if (retryCount < maxRetries) {
                    const waitTime = Math.max(retryAfter * 1000, 5000 + retryCount * 2000);
                    console.log(`⏳ 等待 ${waitTime/1000} 秒后自动重试...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    return searchPddGoods(page, query, retryCount + 1, maxRetries);
                }
            }
            throw new Error(`HTTP错误! 状态: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`✅ 第 ${page} 页数据获取成功:`, data);
        return data;

    } catch (error) {
        if (error.message.includes('429') && retryCount < maxRetries) {
            console.log(`🔄 429错误，${3 + retryCount * 2}秒后重试...`);
            await new Promise(resolve => setTimeout(resolve, (3 + retryCount * 2) * 1000));
            return searchPddGoods(page, query, retryCount + 1, maxRetries);
        }

        console.error(`❌ 请求失败:`, error);
        throw error;
    }
}

// 检查参数状态
function checkAntiContentStatus() {
    if (window._manualAntiContent) {
        const validation = validateAntiContent(window._manualAntiContent);
        console.log('✅ 已设置anti-content参数');
        console.log('📏 参数长度:', window._manualAntiContent.length);
        console.log('🔍 参数预览:', window._manualAntiContent.substring(0, 50) + '...');
        console.log('✅ 验证状态:', validation.valid ? '通过' : validation.reason);
        return true;
    } else {
        console.log('❌ 未设置anti-content参数');
        console.log('💡 请执行 extractFromDevTools() 查看获取步骤');
        return false;
    }
}

// 手动设置anti-content参数的函数（带验证）
function setAntiContent(antiContentValue) {
    if (!antiContentValue) {
        console.error('❌ 参数不能为空');
        console.log('💡 使用方法：setAntiContent("你的anti-content值")');
        return false;
    }

    // 验证参数格式
    const validation = validateAntiContent(antiContentValue);
    if (!validation.valid) {
        console.warn('⚠️ 参数验证警告:', validation.reason);
        console.log('🔍 参数预览:', antiContentValue.substring(0, 100) + '...');
        console.log('💡 如果确认参数正确，可以继续使用');
    } else {
        console.log('✅ 参数验证通过:', validation.reason);
    }

    window._manualAntiContent = antiContentValue;
    console.log('✅ 已设置anti-content参数');
    console.log('� 参数长度:', antiContentValue.length);
    console.log('🔍 参数预览:', antiContentValue.substring(0, 50) + '...');
    console.log('�💡 现在可以使用 searchPddGoods() 或 smartSearch() 进行搜索');

    return true;
}

// 智能搜索函数 - 检查参数后搜索
async function smartSearch(page = 1, query = "糖果") {
    console.log('🧠 启动智能搜索模式...');

    // 检查是否已有anti-content参数
    if (!window._manualAntiContent) {
        console.log('❌ 未检测到anti-content参数');
        console.log('📋 请先获取参数：');
        console.log('1. 执行 extractFromDevTools() 查看详细步骤');
        console.log('2. 或直接执行 setAntiContent("你的参数值")');
        return null;
    }

    console.log('✅ 检测到anti-content参数，开始搜索...');
    return await searchPddGoods(page, query);
}

// 批量搜索多页数据 - 增强版
async function searchMultiplePages(startPage = 1, endPage = 5, query = "糖果", delay = 2000) {
    const results = [];

    console.log(`🚀 开始批量搜索，页数范围：${startPage}-${endPage}，关键词：${query}`);

    for (let page = startPage; page <= endPage; page++) {
        try {
            const result = await searchPddGoods(page, query);
            results.push({
                page: page,
                data: result,
                timestamp: new Date().toISOString()
            });

            // 添加延迟避免请求过于频繁
            if (page < endPage) {
                console.log(`⏳ 等待 ${delay}ms 后继续下一页...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

        } catch (error) {
            console.error(`❌ 第 ${page} 页搜索失败:`, error);
            results.push({
                page: page,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    console.log(`🎉 批量搜索完成！共处理 ${results.length} 页`);
    return results;
}

// 提取商品信息的辅助函数
function extractGoodsInfo(searchResult) {
    if (!searchResult.data || !searchResult.data.result || !searchResult.data.result.goods) {
        console.warn(`⚠️ 第 ${searchResult.page} 页没有商品数据`);
        return [];
    }

    return searchResult.data.result.goods.map(item => ({
        goodsId: item.goodsId,
        goodsName: item.goodsName,
        price: item.price,
        sales: item.sales,
        shopName: item.shopName,
        imageUrl: item.imageUrl,
        page: searchResult.page
    }));
}

// 导出数据为CSV格式
function exportToCSV(goodsList, filename = 'pdd_goods.csv') {
    if (!goodsList || goodsList.length === 0) {
        console.warn('没有数据可导出');
        return;
    }

    const headers = ['商品ID', '商品名称', '价格', '销量', '店铺名称', '图片链接', '页码'];
    const csvContent = [
        headers.join(','),
        ...goodsList.map(item => [
            item.goodsId,
            `"${item.goodsName}"`,
            item.price,
            item.sales,
            `"${item.shopName}"`,
            item.imageUrl,
            item.page
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    console.log(`📁 数据已导出为 ${filename}`);
}

// 使用示例和说明
console.log(`
🎯 拼多多批发搜索脚本已加载！（v2.0 - 智能参数获取版）

📖 推荐使用方法：

🌟 1. 智能搜索（推荐）：
   smartSearch(1, "糖果");  // 自动获取参数并搜索

🌟 2. 智能批量搜索：
   // 先获取参数
   await triggerInternalSearch("糖果");
   // 然后批量搜索
   const results = await searchMultiplePages(1, 3, "糖果", 3000);

📖 传统使用方法：

3. 手动搜索单页：
   searchPddGoods(1, "糖果");

4. 手动批量搜索：
   searchMultiplePages(1, 5, "糖果", 2000);

5. 完整工作流：
   // 搜索并提取商品信息
   const results = await searchMultiplePages(1, 3, "糖果", 3000);
   const allGoods = results.flatMap(extractGoodsInfo);
   console.log('所有商品:', allGoods);

   // 导出为CSV文件
   exportToCSV(allGoods, '糖果商品.csv');

🚨 解决429错误的方法（按推荐程度排序）：

🌟 方法1 - 手动搜索触发（最可靠）：
1. 在拼多多页面上手动搜索一次任意关键词
2. 脚本会自动拦截并捕获anti-content参数
3. 看到"✅ 成功捕获anti-content参数"提示后
4. 使用 searchPddGoods(1, "糖果") 或 smartSearch(1, "糖果")

🌟 方法2 - 从开发者工具复制（100%有效）：
1. 按F12打开开发者工具 → Network标签
2. 在页面上手动搜索一次
3. 找到 'queryGoods' 请求，点击查看详情
4. 在Request Headers中找到 'anti-content'
5. 复制该值，执行：setAntiContent("复制的值");

🌟 方法3 - 一键cURL解析：
1. 在Network面板中右键queryGoods请求
2. 选择 Copy → Copy as cURL
3. 执行：parseAntiContentFromCurl("粘贴cURL命令");

方法4 - 自动触发（可能不稳定）：
   triggerInternalSearch("糖果");  // 尝试自动触发

⚠️  重要注意事项：
- ✅ 脚本已自动拦截网站内部请求，会捕获anti-content参数
- ✅ 推荐使用 smartSearch() 函数，它会自动处理参数获取
- ✅ 脚本会自动重试429错误（最多3次）
- 🔐 anti-content参数现在可以自动获取
- ⏳ 遇到429错误时会自动等待并重试
- 🎯 新版本利用网站内部机制生成参数

🔧 参数说明：
- page: 页码（从1开始）
- query: 搜索关键词
- delay: 请求间隔时间（毫秒，推荐2000-5000）
- startPage/endPage: 批量搜索的页码范围

🛠️ 调试命令大全：

基础搜索：
- smartSearch(page, query) - 智能搜索（推荐）
- searchPddGoods(page, query) - 直接搜索
- searchMultiplePages(start, end, query, delay) - 批量搜索

参数管理：
- setAntiContent("参数值") - 手动设置anti-content
- window._capturedAntiContent - 查看捕获到的参数
- window._capturedHeaders - 查看完整请求头
- window._manualAntiContent - 查看手动设置的参数

参数获取：
- extractFromDevTools() - 显示手动提取步骤
- copyAntiContentFromNetwork() - 显示复制步骤
- parseAntiContentFromCurl("cURL命令") - 从cURL解析参数
- triggerInternalSearch(query) - 尝试自动触发

数据处理：
- extractGoodsInfo(results) - 提取商品信息
- exportToCSV(goodsList, filename) - 导出CSV文件

💡 最佳实践：
1. 先在页面手动搜索一次让脚本捕获参数
2. 使用 smartSearch() 进行搜索
3. 如果还是429错误，使用 extractFromDevTools() 查看手动步骤
`);

// 自动检测当前页面环境
if (window.location.hostname === 'pifa.pinduoduo.com') {
    console.log('✅ 检测到拼多多批发网站环境，脚本可以正常使用');

    // 尝试获取当前页面的搜索关键词
    const urlParams = new URLSearchParams(window.location.search);
    const currentQuery = urlParams.get('word');
    if (currentQuery) {
        console.log(`🔍 检测到当前搜索关键词: ${decodeURIComponent(currentQuery)}`);
        console.log(`💡 快速开始: searchPddGoods(1, "${decodeURIComponent(currentQuery)}")`);
    }
} else {
    console.warn('⚠️  当前不在拼多多批发网站，某些功能可能无法正常工作');
}