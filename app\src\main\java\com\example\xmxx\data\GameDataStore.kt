package com.example.xmxx.data

import android.content.Context
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 使用 DataStore 持久化存储游戏数据（无云端，无声音）
 * - best_score：最高分
 * - saved_board：当前局面（列分隔符 |，空位 .）
 * - saved_score：当前总分
 * - current_level：当前关卡编号（从 1 开始）
 * - level_score：本关累计得分
 * - level_target：本关目标分
 */

private const val DS_NAME = "popstar_game"

val Context.gameDataStore by preferencesDataStore(name = DS_NAME)

object GamePrefsKeys {
    val BEST_SCORE = intPreferencesKey("best_score")
    val SAVED_BOARD = stringPreferencesKey("saved_board")
    val SAVED_SCORE = intPreferencesKey("saved_score")
    // 新增闯关字段
    val CURRENT_LEVEL = intPreferencesKey("current_level")
    val LEVEL_SCORE = intPreferencesKey("level_score")
    val LEVEL_TARGET = intPreferencesKey("level_target")
}

class GameRepository(private val context: Context) {
    // 最高分流
    val bestScore: Flow<Int> = context.gameDataStore.data.map { it[GamePrefsKeys.BEST_SCORE] ?: 0 }

    // 读取已保存的盘面（可能为空字符串）
    val savedBoard: Flow<String> = context.gameDataStore.data.map { it[GamePrefsKeys.SAVED_BOARD] ?: "" }

    val savedScore: Flow<Int> = context.gameDataStore.data.map { it[GamePrefsKeys.SAVED_SCORE] ?: 0 }

    // 关卡相关流
    val currentLevel: Flow<Int> = context.gameDataStore.data.map { it[GamePrefsKeys.CURRENT_LEVEL] ?: 1 }
    val levelScore: Flow<Int> = context.gameDataStore.data.map { it[GamePrefsKeys.LEVEL_SCORE] ?: 0 }
    val levelTarget: Flow<Int> = context.gameDataStore.data.map { it[GamePrefsKeys.LEVEL_TARGET] ?: 0 }

    suspend fun updateBestScore(score: Int) {
        context.gameDataStore.edit { prefs ->
            val old = prefs[GamePrefsKeys.BEST_SCORE] ?: 0
            if (score > old) prefs[GamePrefsKeys.BEST_SCORE] = score
        }
    }

    // 仅保存棋盘与分数（兼容旧调用）
    suspend fun saveGame(boardSerialized: String, score: Int) {
        context.gameDataStore.edit { prefs ->
            prefs[GamePrefsKeys.SAVED_BOARD] = boardSerialized
            prefs[GamePrefsKeys.SAVED_SCORE] = score
        }
    }

    // 保存所有闯关相关字段
    suspend fun saveAll(boardSerialized: String, score: Int, level: Int, levelScore: Int, levelTarget: Int) {
        context.gameDataStore.edit { prefs ->
            prefs[GamePrefsKeys.SAVED_BOARD] = boardSerialized
            prefs[GamePrefsKeys.SAVED_SCORE] = score
            prefs[GamePrefsKeys.CURRENT_LEVEL] = level
            prefs[GamePrefsKeys.LEVEL_SCORE] = levelScore
            prefs[GamePrefsKeys.LEVEL_TARGET] = levelTarget
        }
    }

    suspend fun clearSavedGame() {
        context.gameDataStore.edit { prefs ->
            prefs.remove(GamePrefsKeys.SAVED_BOARD)
            prefs.remove(GamePrefsKeys.SAVED_SCORE)
            prefs.remove(GamePrefsKeys.CURRENT_LEVEL)
            prefs.remove(GamePrefsKeys.LEVEL_SCORE)
            prefs.remove(GamePrefsKeys.LEVEL_TARGET)
        }
    }
}

