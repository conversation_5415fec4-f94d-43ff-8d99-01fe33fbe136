# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个名为"消灭星星(PopStar)"的Android游戏项目，使用Kotlin和Jetpack Compose开发。游戏是经典的消除类游戏，具有关卡系统、分数记录和本地数据持久化功能。

## 技术栈

- **语言**: Kotlin
- **UI框架**: Jetpack Compose (Material 3)
- **Android SDK**: 
  - minSdk: 29 (Android 10)
  - targetSdk: 36 (最新)
  - compileSdk: 36
- **构建工具**: Gradle (Kotlin DSL)
- **数据持久化**: DataStore Preferences
- **依赖管理**: Version Catalog (.toml)

## 项目架构

### 核心模块结构

```
app/src/main/java/com/example/xmxx/
├── MainActivity.kt                 # 主Activity，处理启动画面切换
├── data/
│   └── GameDataStore.kt           # 数据持久化层，使用DataStore
├── game/
│   └── PopStarGame.kt             # 游戏核心逻辑(纯Kotlin，无平台依赖)
├── ui/
│   ├── GameScreen.kt              # 主游戏界面
│   ├── splash/
│   │   └── CyberpunkSplash.kt     # 赛博朋克风格启动画面
│   └── theme/                     # Material 3主题配置
```

### 架构设计原则

- **分层架构**: UI层、数据层、游戏逻辑层分离
- **纯函数游戏引擎**: PopStarGame对象包含所有游戏逻辑，无Android依赖
- **Compose声明式UI**: 完全使用Jetpack Compose构建界面
- **单一数据源**: 使用DataStore进行本地数据持久化

## 常用命令

### 构建和运行
```bash
# 构建Debug版本
./gradlew assembleDebug

# 安装Debug版本到设备
./gradlew installDebug

# 构建Release版本
./gradlew assembleRelease

# 清理构建
./gradlew clean
```

### 测试
```bash
# 运行单元测试
./gradlew testDebugUnitTest

# 运行UI测试(需要连接设备/模拟器)
./gradlew connectedAndroidTest

# 运行所有测试
./gradlew test
```

### 代码检查
```bash
# Kotlin编译检查
./gradlew compileDebugKotlin

# Lint检查
./gradlew lintDebug
```

## 游戏逻辑核心

### PopStarGame对象 (game/PopStarGame.kt)
- **数据结构**: Board = List<MutableList<Int?>>，按列存储便于重力效果
- **核心算法**: 
  - `findGroup()`: 四连通区域搜索
  - `applyMove()`: 消除逻辑+重力+列压缩
  - `hasMoves()`: 判断游戏结束
- **计分规则**: n²×5 (n为消除数量)，残局奖励2000-剩余×5
- **序列化**: 支持棋盘状态的字符串序列化/反序列化

### 关卡系统
- **进度计算**: 起始3000分，每关+1000分
- **状态跟踪**: 当前关卡、本关分数、目标分数
- **数据持久化**: 完整的游戏状态保存/恢复

## UI组件特点

### GameScreen.kt
- **自适应布局**: 使用Card+AspectRatio确保棋盘正方形
- **交互模式**: 单击选中+确认消除，双击直接消除
- **动画效果**: 爆裂粒子效果，状态过渡动画
- **响应式设计**: 支持不同屏幕尺寸

### CyberpunkSplash.kt  
- **赛博朋克风格**: 霓虹渐变、扫描线、粒子系统
- **性能优化**: 控制粒子数量，使用高效动画
- **交互友好**: 支持点击跳过

## 数据持久化

### GameDataStore.kt
使用DataStore Preferences存储：
- `best_score`: 最高分记录
- `saved_board`: 当前棋盘状态(序列化字符串)
- `saved_score`: 当前总分
- `current_level`: 当前关卡
- `level_score`: 本关得分
- `level_target`: 本关目标分

## 开发注意事项

### Compose最佳实践
- 使用`remember`缓存计算结果
- `LaunchedEffect`处理副作用
- 状态提升到合适层级
- 避免不必要的重组

### 性能考虑
- 粒子系统数量控制(48个粒子)
- LazyVerticalGrid禁用滚动
- 使用aspectRatio确保正确布局
- 动画使用合适的easing函数

### 测试策略
- 游戏逻辑单元测试(PopStarGame)
- UI交互测试(GameScreen)
- 数据持久化测试(DataStore)

## 版本信息

- **Android Gradle Plugin**: 8.11.1
- **Kotlin**: 2.0.21
- **Compose BOM**: 2024.09.00
- **DataStore**: 1.1.1

当前项目针对最新Android版本优化，使用最新的Jetpack Compose和Material 3设计规范。