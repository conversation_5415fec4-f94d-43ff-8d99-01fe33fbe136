package com.example.xmxx.ui

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.Canvas
import androidx.compose.ui.geometry.Offset
import com.example.xmxx.data.GameRepository
import com.example.xmxx.game.PopStarGame
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.PI

// 爆裂粒子模型
data class BurstParticle(var x: Float, var y: Float, val vx: Float, val vy: Float, val color: Color, var life: Long)

// 全局粒子状态
private var burstParticles: List<BurstParticle> by mutableStateOf(emptyList())

// 触发一次爆裂：围绕格子中心生成一圈粒子（需要传入屏幕坐标）
private fun triggerBurst(screenX: Float, screenY: Float, baseColor: Color) {
    val n = 20 // 增加粒子数量让效果更明显
    val list = mutableListOf<BurstParticle>()
    for (i in 0 until n) {
        val ang = (2 * PI * i / n).toFloat()
        val speed = 8f + (0..3).random() * 2f // 随机速度让效果更自然
        list += BurstParticle(
            x = screenX, y = screenY,
            vx = cos(ang) * speed, 
            vy = sin(ang) * speed, // 使用正值让粒子向各个方向飞散
            color = baseColor.copy(alpha = 0.9f), 
            life = 300L + (0..100).random().toLong() // 随机生命周期
        )
    }
    burstParticles = burstParticles + list
}

// 将颜色索引映射到显示颜色
@Composable
private fun colorFor(index: Int?): Color = when (index) {
    null -> Color.Transparent
    0 -> Color(0xFFFF5722) // 红色
    1 -> Color(0xFF2196F3) // 蓝色
    2 -> Color(0xFF4CAF50) // 绿色
    3 -> Color(0xFFFFEB3B) // 黄色
    4 -> Color(0xFF9C27B0) // 紫色
    else -> Color(0xFFFF9800) // 橙色
}

/**
 * 游戏界面（Jetpack Compose）
 * 要求：单机、无声音、本地持久化
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun GameScreen(
    modifier: Modifier = Modifier,
    gridSize: Int = 10,
    colorsCount: Int = 5,
    cellSize: Dp = 36.dp,
) {
    val ctx = LocalContext.current
    val repo = remember { GameRepository(ctx) }

    // 状态：棋盘、总分、最高分、选中的组 + 闯关信息
    var board by remember { mutableStateOf(PopStarGame.newBoard(gridSize, gridSize, colorsCount)) }
    var score by remember { mutableStateOf(0) }
    var selectedGroup by remember { mutableStateOf(emptySet<PopStarGame.Pos>()) }
    val bestScore by repo.bestScore.collectAsState(initial = 0)

    // 闯关状态：当前关、本关分数、本关目标、通关/失败标记
    var level by remember { mutableStateOf(1) }
    var levelScore by remember { mutableStateOf(0) }
    var levelTarget by remember { mutableStateOf(0) }
    var levelCleared by remember { mutableStateOf(false) }
    var levelFailed by remember { mutableStateOf(false) }
    var showFailDialog by remember { mutableStateOf(false) }

    fun computeLevelTarget(lvl: Int): Int {
        // 新计分适配：每次消除为 n^2*5，整体分数更高，因此提高关卡目标：起始 3000，每关 +1000
        // 可根据反馈灵活调整节奏
        return 1000 + (lvl - 1) * 1000
    }

    // 启动时恢复一次存档，并迁移旧的关卡目标到新标准
    LaunchedEffect(Unit) {
        val sb = repo.savedBoard.first()
        if (sb.isNotBlank()) board = PopStarGame.deserialize(sb)
        val s = repo.savedScore.first(); if (s > 0) score = s
        val lv = repo.currentLevel.first(); if (lv > 0) level = lv
        val lvs = repo.levelScore.first(); if (lvs > 0) levelScore = lvs
        val lvt = repo.levelTarget.first()
        levelTarget = when {
            lvt <= 0 -> computeLevelTarget(level)
            // 旧版本小目标（<1000）视为旧制，迁移到新标准
            lvt < 1000 -> computeLevelTarget(level)
            else -> lvt
        }
    }

    val scope = rememberCoroutineScope()

    fun saveAll() = scope.launch {
        repo.saveAll(
            PopStarGame.serialize(board),
            score,
            level,
            levelScore,
            levelTarget
        )
        repo.updateBestScore(score)
    }

    fun newGame() = scope.launch {
        level = 1
        levelScore = 0
        levelTarget = computeLevelTarget(level)
        board = PopStarGame.newBoard(gridSize, gridSize, colorsCount)
        score = 0
        selectedGroup = emptySet()
        levelCleared = false; levelFailed = false
        repo.clearSavedGame()
    }

    fun nextLevel() = scope.launch {
        level += 1
        levelScore = 0
        levelTarget = computeLevelTarget(level)
        board = PopStarGame.newBoard(gridSize, gridSize, colorsCount)
        selectedGroup = emptySet()
        levelCleared = false; levelFailed = false
        saveAll()
    }

    fun retryLevel() = scope.launch {
        levelScore = 0
        board = PopStarGame.newBoard(gridSize, gridSize, colorsCount)
        selectedGroup = emptySet()
        levelCleared = false; levelFailed = false
        saveAll()
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(12.dp),
        verticalArrangement = Arrangement.Top // 改为顶部对齐，让内容从上往下排列
    ) {
        // 分数显示区域 - 添加卡片背景和更好的样式
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(72.dp), // 更高一些容纳关卡进度
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(Modifier.fillMaxSize().padding(horizontal = 12.dp, vertical = 8.dp)) {
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                    Text("分数：$score", style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer, fontWeight = FontWeight.Bold)
                    Text("最高分：$bestScore", style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer, fontWeight = FontWeight.Bold)
                }
                Spacer(Modifier.height(6.dp))
                // 关卡 + 进度
                val progress = (levelScore.toFloat() / levelTarget.coerceAtLeast(1)).coerceIn(0f, 1f)
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                    Text("关卡：$level", style = MaterialTheme.typography.labelLarge,
                        color = MaterialTheme.colorScheme.onPrimaryContainer, fontWeight = FontWeight.Bold)
                    Text("目标：$levelScore / $levelTarget", style = MaterialTheme.typography.labelLarge,
                        color = MaterialTheme.colorScheme.onPrimaryContainer)
                }
                LinearProgressIndicator(progress = progress, modifier = Modifier.fillMaxWidth().height(6.dp))
            }
        }

        // 游戏棋盘区域 - 添加卡片背景和更好的间距，限制高度
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f) // 保持正方形比例
                .padding(vertical = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Box {
                // 使用 LazyVerticalGrid 绘制网格（注意：我们的 Board 是按列存储，这里转成按行渲染）
                val w = PopStarGame.width(board)
                val h = PopStarGame.columnHeight(board)
                val items = remember(board) {
                    // 展开为行优先渲染的列表 (y 从高到低更直观，但这里用从高到低/或从低到高均可)
                    buildList {
                        for (y in h - 1 downTo 0) {
                            for (x in 0 until w) add(Pair(x, y))
                        }
                    }
                }
                LazyVerticalGrid(
                    columns = GridCells.Fixed(w),
                    modifier = Modifier
                        .fillMaxSize(), // 填满卡片可用空间（卡片已是正方形）
                    userScrollEnabled = false,
                    contentPadding = PaddingValues(6.dp), // 棋盘内边距，避免贴边被圆角裁切
                    horizontalArrangement = Arrangement.spacedBy(2.dp), // 列间距
                    verticalArrangement = Arrangement.spacedBy(2.dp), // 行间距
                ) {
                    itemsIndexed(items) { _, (x, y) ->
                        val v = PopStarGame.get(board, x, y)
                        val pos = PopStarGame.Pos(x, y)
                        val isSelected = pos in selectedGroup
                        val color = colorFor(v)

                        Box(
                            modifier = Modifier
                                .fillMaxWidth() // 每个网格单元宽度自适应
                                .aspectRatio(1f) // 保证为正方形
                                .padding(1.dp)
                                .clip(RoundedCornerShape(6.dp))
                                .background(
                                    if (v != null) {
                                        if (isSelected) {
                                            // 选中时：颜色更亮一些
                                            color.copy(alpha = 0.9f)
                                        } else {
                                            // 正常时：稍微透明的颜色背景
                                            color.copy(alpha = 0.7f)
                                        }
                                    } else {
                                        Color.Transparent
                                    }
                                )
                                .border(
                                    width = if (isSelected) 3.dp else 0.dp,
                                    color = Color.White,
                                    shape = RoundedCornerShape(6.dp)
                                )
                                .pointerInput(Unit) {
                                    detectTapGestures(
                                        onDoubleTap = { offset ->
                                            val group = PopStarGame.findGroup(board, x, y)
                                            if (group.size >= 2 && !levelCleared && !levelFailed) {
                                                // 为组内每个星星触发爆裂粒子效果
                                                group.forEach { pos ->
                                                    // 计算该星星在屏幕上的大概位置
                                                    // 这里使用点击位置作为基准，为同一组的其他星星添加偏移
                                                    val deltaX = (pos.x - x) * size.width / 10f // 假设10x10网格
                                                    val deltaY = (pos.y - y) * size.height / 10f
                                                    triggerBurst(
                                                        offset.x + deltaX, 
                                                        offset.y + deltaY, 
                                                        color
                                                    )
                                                }

                                                val (nb, gain, removed) = PopStarGame.applyMove(board, x, y)
                                                board = nb
                                                score += gain
                                                levelScore += gain
                                                selectedGroup = emptySet()

                                                val hasMoves = PopStarGame.hasMoves(board)
                                                if (!hasMoves) {
                                                    val remaining = PopStarGame.remainingCount(board)
                                                    if (remaining < 10) {
                                                        val finalBonus = 2000 - remaining * 5
                                                        score += finalBonus
                                                        levelScore += finalBonus
                                                    }
                                                    if (levelScore >= levelTarget) levelCleared = true else { levelFailed = true; showFailDialog = true }
                                                }
                                                saveAll()
                                            }
                                        },
                                        onTap = {
                                            val group = PopStarGame.findGroup(board, x, y)
                                            if (group.size >= 2 && !levelCleared && !levelFailed) {
                                                if (selectedGroup == group) {
                                                    val (nb, gain, removed) = PopStarGame.applyMove(board, x, y)
                                                    board = nb
                                                    score += gain
                                                    levelScore += gain
                                                    selectedGroup = emptySet()

                                                    val hasMoves = PopStarGame.hasMoves(board)
                                                    if (!hasMoves) {
                                                        val remaining = PopStarGame.remainingCount(board)
                                                        if (remaining < 10) {
                                                            val finalBonus = 2000 - remaining * 5
                                                            score += finalBonus
                                                            levelScore += finalBonus
                                                        }
                                                        if (levelScore >= levelTarget) levelCleared = true else { levelFailed = true; showFailDialog = true }
                                                    }
                                                    saveAll()
                                                } else {
                                                    selectedGroup = group
                                                }
                                            } else if (group.size < 2) {
                                                selectedGroup = emptySet()
                                            }
                                        }
                                    )
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            if (v != null) {
                                Icon(
                                    imageVector = Icons.Default.Star,
                                    contentDescription = null,
                                    tint = if (isSelected) Color.White else Color.White.copy(alpha = 0.9f),
                                    modifier = Modifier.fillMaxSize(0.78f) // 图标相对单元格缩放
                                )
                            }
                        }
                    }
                }

                // 在棋盘区域上方绘制爆裂粒子层
                Canvas(Modifier.fillMaxSize()) {
                    // 更新与绘制粒子
                    burstParticles = burstParticles.filter { it.life > 0 }
                    val updated = mutableListOf<BurstParticle>()
                    burstParticles.forEach { p ->
                        val nx = p.x + p.vx
                        val ny = p.y + p.vy
                        
                        // 根据生命周期计算透明度和大小
                        val lifeRatio = p.life / 300f // 归一化生命周期
                        val alpha = (lifeRatio * 0.9f).coerceIn(0f, 1f)
                        val radius = (4f - lifeRatio * 2f).coerceAtLeast(1f)
                        
                        // 绘制粒子主体（圆形）
                        drawCircle(
                            color = p.color.copy(alpha = alpha), 
                            radius = radius, 
                            center = Offset(nx, ny)
                        )
                        
                        // 绘制粒子光晕效果（较大的半透明圆）
                        if (alpha > 0.3f) {
                            drawCircle(
                                color = p.color.copy(alpha = alpha * 0.3f), 
                                radius = radius * 2f, 
                                center = Offset(nx, ny)
                            )
                        }
                        
                        // 添加轻微的重力效果和阻力
                        val newVx = p.vx * 0.98f // 阻力
                        val newVy = p.vy * 0.98f + 0.2f // 重力
                        
                        updated += p.copy(
                            x = nx, 
                            y = ny, 
                            vx = newVx,
                            vy = newVy,
                            life = p.life - 8 // 减慢消失速度
                        )
                    }
                    burstParticles = updated
                }
            }
        }

        // 按钮区域 - 使用卡片包装，更好的布局，确保不被挤压
        // 添加间距
        Spacer(modifier = Modifier.height(8.dp))

        // 按钮区域 - 闯关控制
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(), // 自适应内容高度，避免第二排按钮被裁切
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = { newGame() },
                        modifier = Modifier.weight(1f).height(40.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary)
                    ) { Text("新游戏", style = MaterialTheme.typography.bodyMedium, fontWeight = FontWeight.Bold) }

                    Button(
                        onClick = { saveAll() },
                        modifier = Modifier.weight(1f).height(40.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary)
                    ) { Text("保存进度", style = MaterialTheme.typography.bodyMedium, fontWeight = FontWeight.Bold) }
                }
                Spacer(Modifier.height(6.dp))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(12.dp)) {
                    Button(
                        onClick = { if (levelCleared) nextLevel() }, enabled = levelCleared,
                        modifier = Modifier.weight(1f).height(36.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary,
                            contentColor = MaterialTheme.colorScheme.onPrimary,
                            disabledContainerColor = MaterialTheme.colorScheme.surface, // 不可用时也要清晰
                            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.65f)
                        )
                    ) { Text("下一关") }
                    Button(
                        onClick = { if (levelFailed) retryLevel() }, enabled = levelFailed,
                        modifier = Modifier.weight(1f).height(36.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error,
                            contentColor = MaterialTheme.colorScheme.onError,
                            disabledContainerColor = MaterialTheme.colorScheme.surface,
                            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.65f)
                        )
                    ) { Text("重试本关") }
                }
            }
        }

        // 游戏状态信息区域 - 确保不被挤压
        if (selectedGroup.isNotEmpty() || !PopStarGame.hasMoves(board)) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight(), // 确保高度适应内容
                colors = CardDefaults.cardColors(
                    containerColor = if (selectedGroup.isNotEmpty())
                        MaterialTheme.colorScheme.primaryContainer
                    else
                        MaterialTheme.colorScheme.errorContainer
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp), // 减少内边距
                    verticalArrangement = Arrangement.spacedBy(6.dp) // 减少间距
                ) {
                    // 显示选中组的信息
                    if (selectedGroup.isNotEmpty()) {
                        Text(
                            text = "已选中 ${selectedGroup.size} 个星星",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "再次点击消除！得分：${selectedGroup.size * selectedGroup.size * 5}",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    // 游戏结束提示
                    val hasMoves = PopStarGame.hasMoves(board)
                    if (!hasMoves) {
                        if (levelCleared) {
                            Text("✔ 本关通关！点击下方「下一关」继续~", style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer, fontWeight = FontWeight.Bold)
                        } else if (levelFailed) {
                            Text("未达目标分：$levelScore / $levelTarget，点击「重试本关」再来！", style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onErrorContainer, fontWeight = FontWeight.Bold)
                        } else {
                            // 首次到达无可走位置时根据目标自动显示
                            if (levelScore >= levelTarget) {
                                levelCleared = true
                                Text("✔ 本关通关！点击下方「下一关」继续~", style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer, fontWeight = FontWeight.Bold)
                            } else {
                                levelFailed = true
                                showFailDialog = true
                                Text("未达目标分：$levelScore / $levelTarget，点击「重试本关」再来！", style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onErrorContainer, fontWeight = FontWeight.Bold)
                            }
                        }
                    }
                }
            }
        }
    }

    // 失败弹窗
    if (showFailDialog) {
        AlertDialog(
            onDismissRequest = { showFailDialog = false },
            confirmButton = {
                TextButton(onClick = { showFailDialog = false; retryLevel() }) { Text("重试本关") }
            },
            dismissButton = {
                TextButton(onClick = { showFailDialog = false }) { Text("知道了") }
            },
            title = { Text("挑战失败") },
            text = { Text("未达到过关分数：$levelScore / $levelTarget") }
        )
    }
}