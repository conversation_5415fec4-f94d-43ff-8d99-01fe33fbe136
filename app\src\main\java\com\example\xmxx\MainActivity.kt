package com.example.xmxx

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.example.xmxx.ui.GameScreen
import com.example.xmxx.ui.splash.CyberpunkSplash
import com.example.xmxx.ui.theme.XmxxTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            XmxxTheme {
                Surface(modifier = Modifier.fillMaxSize()) {
                    // 显示开屏 -> 游戏 的动画过渡
                    var showSplash by remember { mutableStateOf(true) }

                    AnimatedContent(
                        targetState = showSplash,
                        transitionSpec = { fadeIn(tween(400)) togetherWith fadeOut(tween(400)) },
                        label = "splashTransition"
                    ) { isSplash ->
                        if (isSplash) {
                            CyberpunkSplash(onFinished = { showSplash = false })
                        } else {
                            GameScreen()
                        }
                    }
                }
            }
        }
    }
}
