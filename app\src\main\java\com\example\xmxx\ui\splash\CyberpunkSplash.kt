package com.example.xmxx.ui.splash

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.random.Random

/**
 * 赛博朋克风格开屏动画
 * - 霓虹渐变背景 + 扫描线 + 视差网格
 * - 中央 LOGO 文本带霓虹发光与故障(Glitch)偏移
 * - 粒子火花漂移
 * - 2.8s 自动结束，或点击跳过
 */
@Composable
fun CyberpunkSplash(
    modifier: Modifier = Modifier,
    durationMillis: Int = 2800,
    onFinished: () -> Unit,
) {
    val context = LocalContext.current

    // 颜色：赛博紫、霓虹蓝、电子粉
    val neon1 = Color(0xFF6C00FF)
    val neon2 = Color(0xFF00E5FF)
    val neon3 = Color(0xFFFF2D95)

    // 背景渐变动画：左右缓慢偏移
    val infinite = rememberInfiniteTransition(label = "bg")
    val shift by infinite.animateFloat(
        initialValue = -0.25f,
        targetValue = 0.25f,
        animationSpec = infiniteRepeatable(animation = tween(2200, easing = LinearEasing), repeatMode = RepeatMode.Reverse),
        label = "shift"
    )

    // LOGO 轻微呼吸放大
    val scale by infinite.animateFloat(
        initialValue = 0.96f,
        targetValue = 1.04f,
        animationSpec = infiniteRepeatable(animation = tween(1200, easing = FastOutSlowInEasing), repeatMode = RepeatMode.Reverse),
        label = "scale"
    )

    // 粒子
    val particles = remember { mutableStateListOf<Particle>() }
    LaunchedEffect(Unit) {
        val rnd = Random(System.currentTimeMillis())
        repeat(48) { // 适中数量，性能友好
            particles += Particle(
                x = rnd.nextFloat(),
                y = rnd.nextFloat(),
                vx = (rnd.nextFloat() - 0.5f) * 0.02f,
                vy = -0.05f - rnd.nextFloat() * 0.05f,
                size = 1.5f + rnd.nextFloat() * 3f,
                hue = listOf(neon1, neon2, neon3)[rnd.nextInt(3)]
            )
        }
    }

    // 故障(Glitch)抖动：以节拍间歇产生
    var glitchOffset by remember { mutableStateOf(Offset.Zero) }
    var glitchColorShift by remember { mutableStateOf(0f) }
    LaunchedEffect(Unit) {
        val rnd = Random(System.currentTimeMillis())
        while (true) {
            delay(220)
            val happen = rnd.nextFloat() < 0.45f
            if (happen) {
                glitchOffset = Offset((rnd.nextFloat() - 0.5f) * 12f, (rnd.nextFloat() - 0.5f) * 6f)
                glitchColorShift = rnd.nextFloat()
                delay(60)
                glitchOffset = Offset.Zero
            }
        }
    }

    // 计时结束
    LaunchedEffect(Unit) {
        delay(durationMillis.toLong())
        onFinished()
    }

    // 点击跳过
    val skip = rememberUpdatedState(newValue = onFinished)

    Box(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(Unit) { detectTapGestures(onTap = { skip.value.invoke() }) }
            .drawBehind {
                // 1) 霓虹渐变背景
                val w = size.width
                val h = size.height
                val start = Offset(w * (0.25f + shift), 0f)
                val end = Offset(w * (0.75f + shift), h)
                drawRect(Brush.linearGradient(listOf(neon1, neon2, neon3), start, end))

                // 2) 视差网格（半透明线）
                val gridColor = Color.White.copy(alpha = 0.06f)
                val step = max(24f, min(w, h) / 18f)
                val offset = (System.currentTimeMillis() % 1800L).toFloat() / 1800f * step
                var x = -offset
                while (x < w + step) {
                    drawLine(gridColor, Offset(x, 0f), Offset(x, h), strokeWidth = 1f)
                    x += step
                }
                var y = -offset
                while (y < h + step) {
                    drawLine(gridColor, Offset(0f, y), Offset(w, y), strokeWidth = 1f)
                    y += step
                }

                // 3) 扫描线 Overlay
                val lineH = 3f
                var yy = 0f
                while (yy < h) {
                    drawRect(Color.Black.copy(alpha = 0.06f), topLeft = Offset(0f, yy), size = androidx.compose.ui.geometry.Size(w, lineH))
                    yy += lineH * 2
                }
            }
    ) {
        // 粒子层
        Canvas(Modifier.fillMaxSize()) {
            val w = size.width
            val h = size.height
            // 更新粒子
            val t = 1 / 60f
            particles.forEach { p ->
                p.x += p.vx * t
                p.y += p.vy * t
                if (p.y < -0.1f) { // 从底部重生
                    p.y = 1.1f
                    p.x = (p.x + 0.3f) % 1f
                }
            }
            particles.forEach { p ->
                val px = p.x * w
                val py = p.y * h
                drawCircle(color = p.hue.copy(alpha = 0.75f), radius = p.size, center = Offset(px, py))
                // 拖尾
                drawLine(
                    color = p.hue.copy(alpha = 0.35f),
                    start = Offset(px, py + 10),
                    end = Offset(px, py + 22),
                    strokeWidth = 2f,
                    cap = StrokeCap.Round
                )
            }
        }

        // 中央 LOGO 卡片（带轻微玻璃化）
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Card(
                modifier = Modifier
                    .widthIn(min = 220.dp)
                    .padding(horizontal = 24.dp)
                    .clip(RoundedCornerShape(18.dp))
                    .alpha(0.9f),
                colors = CardDefaults.cardColors(containerColor = Color(0x22FFFFFF)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Column(
                    Modifier
                        .padding(vertical = 32.dp, horizontal = 24.dp)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    NeonTitle(
                        text = "XMXX",
                        scale = scale,
                        colorShift = glitchColorShift,
                        glitch = glitchOffset
                    )
                    Spacer(Modifier.height(8.dp))
                    AnimatedSubtitle()
                }
            }
        }
    }
}

// 简单粒子模型
private data class Particle(
    var x: Float, // 0..1
    var y: Float, // 0..1
    var vx: Float,
    var vy: Float,
    var size: Float,
    var hue: Color,
)

// 霓虹标题：多层叠加制造发光 + RGB 偏移
@Composable
private fun NeonTitle(text: String, scale: Float, colorShift: Float, glitch: Offset) {
    val base = Color(0xFFDBEAFE) // 淡蓝
    val glowA = Color(0xFF22D3EE)
    val glowB = Color(0xFFF472B6)

    Box(modifier = Modifier.wrapContentSize(), contentAlignment = Alignment.Center) {
        // 背景发光圈
        Text(
            text = text,
            fontSize = 44.sp,
            fontWeight = FontWeight.Black,
            color = Color.White.copy(alpha = 0.85f),
            modifier = Modifier
                .graphicsLayer {
                    scaleX = scale; scaleY = scale
                    shadowElevation = 24f
                    // 用 RenderEffect 模拟光晕（Compose 会自动降级处理）
                }
                .drawBehind {
                    val r = size.maxDimension / 2f
                    drawCircle(Color(0x6622D3EE), radius = r * 0.85f)
                    drawCircle(Color(0x33F472B6), radius = r)
                },
            textAlign = TextAlign.Center
        )
        // RGB 偏移产生故障效果
        val dx = glitch.x
        val dy = glitch.y
        Text(
            text = text,
            fontSize = 44.sp,
            fontWeight = FontWeight.Black,
            color = glowA.copy(alpha = 0.75f),
            modifier = Modifier.offset { androidx.compose.ui.unit.IntOffset(dx.toInt() / 2, dy.toInt() / 2) }
        )
        Text(
            text = text,
            fontSize = 44.sp,
            fontWeight = FontWeight.Black,
            color = glowB.copy(alpha = 0.75f),
            modifier = Modifier.offset { androidx.compose.ui.unit.IntOffset(-dx.toInt() / 2, -dy.toInt() / 2) }
        )
        Text(
            text = text,
            fontSize = 44.sp,
            fontWeight = FontWeight.ExtraBold,
            color = base
        )
    }
}

@Composable
private fun AnimatedSubtitle() {
    val phrases = listOf("CYBERPUNK", "NEON", "GLITCH", "HYPERDRIVE")
    var index by remember { mutableStateOf(0) }
    LaunchedEffect(Unit) {
        while (true) {
            delay(420)
            index = (index + 1) % phrases.size
        }
    }
    val alpha by animateFloatAsState(targetValue = 1f, animationSpec = tween(300), label = "alpha")
    Text(
        text = phrases[index],
        fontSize = 14.sp,
        color = Color.White.copy(alpha = 0.82f),
        modifier = Modifier.alpha(alpha),
        letterSpacing = 6.sp
    )
}

